<template>
  <div class="app-container">
    <!-- 搜索筛选 -->
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        placeholder="请输入订单号或用户账号"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter="handleFilter"
      />
      <el-select
        v-model="listQuery.status"
        placeholder="订单状态"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option label="已取消" :value="0" />
        <el-option label="已下单" :value="1" />
        <el-option label="已取件" :value="2" />
        <el-option label="待检测" :value="3" />
        <el-option label="已检测" :value="4" />
        <el-option label="已确认" :value="5" />
        <el-option label="订单已完成" :value="6" />
      </el-select>
      <el-select
        v-model="listQuery.goldType"
        placeholder="贵金属类型"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option label="黄金" value="jewelry" />
        <el-option label="铂金" value="bar" />
        <el-option label="钯金" value="broken" />
        <el-option label="其他" value="other" />
      </el-select>
      <el-input-number
        v-model="listQuery.estimatedPrice"
        placeholder="预估价格"
        :precision="2"
        :min="0"
        style="width: 150px"
        class="filter-item"
        controls-position="right"
        @keyup.enter="handleFilter"
      />
      <el-button
        class="filter-item"
        type="primary"
        icon="Search"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        icon="Refresh"
        @click="handleReset"
      >
        重置
      </el-button>
      <el-button
        class="filter-item"
        type="success"
        icon="Download"
        @click="handleDownload"
      >
        导出
      </el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="加载中"
      border
      fit
      highlight-current-row
    >
      <el-table-column label="订单号" width="300">
        <template #default="scope">
          <el-link type="primary" @click="handleDetail(scope.row)">
            {{ scope.row.orderId }}
          </el-link>
        </template>
      </el-table-column>

      <el-table-column label="用户账号" width="auto">
        <template #default="scope">
          {{ scope.row.account }}
        </template>
      </el-table-column>

      <el-table-column label="贵金属类型" width="100">
        <template #default="scope">
          {{ getGoldTypeText(scope.row.goldType) }}
        </template>
      </el-table-column>

      <el-table-column label="成色" width="80">
        <template #default="scope">
          {{ scope.row.purity }}
        </template>
      </el-table-column>

      <el-table-column label="预估重量(g)" width="120" align="right">
        <template #default="scope">
          {{ scope.row.estimatedWeight }}
        </template>
      </el-table-column>

      <el-table-column label="预估价格" width="120" align="right">
        <template #default="scope">
          ¥{{ scope.row.estimatedPrice }}
        </template>
      </el-table-column>

      <el-table-column label="最终价格" width="120" align="right">
        <template #default="scope">
          <span v-if="scope.row.finalPrice">¥{{ scope.row.finalPrice }}</span>
          <span v-else class="text-muted">-</span>
        </template>
      </el-table-column>

      <el-table-column label="状态" width="100" align="center">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="创建时间" width="160">
        <template #default="scope">
          {{ formatTime(scope.row.createTime) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="300" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button type="primary" size="small" @click="handleDetail(scope.row)">
            详情
          </el-button>
          <el-button
            v-if="scope.row.status === 1"
            size="small"
            type="success"
            @click="handlePickup(scope.row)"
          >
            取件
          </el-button>
          <el-button
            v-if="scope.row.status === 2"
            size="small"
            type="warning"
            @click="handleInspect(scope.row)"
          >
            检测
          </el-button>
          <el-button
            v-if="scope.row.status === 3"
            size="small"
            type="primary"
            @click="handleUploadResult(scope.row)"
          >
            上传结果
          </el-button>
          <el-button
            v-if="scope.row.status === 5"
            size="small"
            type="success"
            @click="handleComplete(scope.row)"
          >
            完成订单
          </el-button>
          <!-- 已送到店按钮 - 所有状态都显示 -->
          <el-button
            size="small"
            type="success"
            @click="handleDeliveredToStore(scope.row)"
          >
            已送到店
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNum"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <!-- 上传检测结果对话框 -->
    <el-dialog
      title="上传检测结果"
      v-model="processDialogVisible"
      width="500px"
    >
      <el-form
        ref="processFormRef"
        :model="processForm"
        label-width="100px"
      >
        <el-form-item label="实际重量(g)">
          <el-input-number
            v-model="processForm.actualWeight"
            :precision="2"
            :min="0"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="实际成色">
          <el-input v-model="processForm.actualPurity" />
        </el-form-item>
        <el-form-item label="最终价格">
          <el-input-number
            v-model="processForm.finalPrice"
            :precision="2"
            :min="0"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="检测结果">
          <el-input
            v-model="processForm.inspectionResult"
            type="textarea"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="processDialogVisible = false">
            取消
          </el-button>
          <el-button type="primary" @click="confirmProcess">
            上传结果
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 已送到店对话框 -->
    <el-dialog
      title="设置最终价格"
      v-model="deliveredDialogVisible"
      width="400px"
    >
      <el-form
        ref="deliveredFormRef"
        :model="deliveredForm"
        :rules="deliveredRules"
        label-width="100px"
      >
        <el-form-item label="订单号">
          <el-input v-model="deliveredForm.orderId" readonly />
        </el-form-item>
        <el-form-item label="最终价格" prop="finalPrice">
          <el-input-number
            v-model="deliveredForm.finalPrice"
            :precision="2"
            :min="0"
            style="width: 100%"
            placeholder="请输入最终价格"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="deliveredDialogVisible = false">
            取消
          </el-button>
          <el-button type="primary" @click="confirmDelivered" :loading="deliveredLoading">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getOrderList, updateOrderStatus, setFinalPrice, exportOrders } from '@/api/orders'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination/index.vue'

const router = useRouter()

const list = ref([])
const total = ref(0)
const listLoading = ref(true)
const listQuery = ref({
  pageNum: 1,
  pageSize: 20,
  keyword: '',
  status: '',
  goldType: '',
  estimatedPrice: null
})

const processDialogVisible = ref(false)
const processForm = ref({
  orderId: '',
  actualWeight: 0,
  actualPurity: '',
  finalPrice: 0,
  inspectionResult: ''
})
const currentOrder = ref(null)

// 已送到店对话框相关
const deliveredDialogVisible = ref(false)
const deliveredLoading = ref(false)
const deliveredForm = ref({
  orderId: '',
  finalPrice: null
})
const deliveredRules = {
  finalPrice: [
    { required: true, message: '请输入最终价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '最终价格必须大于等于0', trigger: 'blur' }
  ]
}

async function getList() {
  listLoading.value = true
  try {
    // 构建请求参数，过滤空值
    const params = {}
    Object.keys(listQuery.value).forEach(key => {
      const value = listQuery.value[key]
      if (value !== null && value !== undefined && value !== '') {
        params[key] = value
      }
    })

    const response = await getOrderList(params)

    // 适配后端返回的IPage结构
    if (response.data) {
      // 处理分页数据结构
      if (response.data.records) {
        // IPage结构：{records: [], total: number, size: number, current: number}
        list.value = response.data.records || []
        total.value = response.data.total || 0
      } else if (response.data.list) {
        // 兼容其他可能的结构
        list.value = response.data.list || []
        total.value = response.data.total || 0
      } else if (Array.isArray(response.data)) {
        // 直接返回数组的情况
        list.value = response.data
        total.value = response.data.length
      } else {
        list.value = []
        total.value = 0
      }
    } else {
      list.value = []
      total.value = 0
    }

    console.log('获取回收订单列表成功:', {
      total: total.value,
      count: list.value.length,
      params,
      responseStructure: response.data ? Object.keys(response.data) : 'no data'
    })
  } catch (error) {
    console.error('获取回收订单列表失败:', error)
    console.error('请求参数:', params)
    console.error('错误详情:', error.response?.data || error.message)
    ElMessage.error(`获取订单列表失败: ${error.response?.data?.message || error.message || '请稍后重试'}`)

    // 开发环境下显示模拟数据
    if (process.env.NODE_ENV === 'development') {
      list.value = [
        {
          id: 1,
          orderId: 'REC20231201001',
          account: 'user001',
          goldType: 'jewelry', // 黄金
          goldCondition: '完好',
          purity: '足金',
          estimatedWeight: 10.5,
          estimatedPrice: 1500.00,
          finalPrice: null,
          status: 1, // 已下单
          description: '黄金戒指',
          inspectionResult: '',
          receiverName: '张三',
          receiverPhone: '***********',
          receiverAddress: '北京市朝阳区xxx',
          createTime: '2025-06-04T13:47:17',
          updateTime: '2025-06-04T13:47:17'
        },
        {
          id: 2,
          orderId: 'REC20231201002',
          account: 'user002',
          goldType: 'bar', // 铂金
          goldCondition: '完好',
          purity: '999',
          estimatedWeight: 50.0,
          estimatedPrice: 7200.00,
          finalPrice: null,
          status: 2, // 已取件
          description: '铂金条',
          inspectionResult: '',
          receiverName: '李四',
          receiverPhone: '***********',
          receiverAddress: '上海市浦东新区xxx',
          createTime: '2025-06-03T13:47:17',
          updateTime: '2025-06-04T13:47:17'
        },
        {
          id: 3,
          orderId: 'REC20231201003',
          account: 'user003',
          goldType: 'broken', // 钯金
          goldCondition: '完好',
          purity: '999',
          estimatedWeight: 31.1,
          estimatedPrice: 4500.00,
          finalPrice: null,
          status: 3, // 待检测
          description: '钯金饰品',
          inspectionResult: '',
          receiverName: '王五',
          receiverPhone: '***********',
          receiverAddress: '广州市天河区xxx',
          createTime: '2025-06-02T13:47:17',
          updateTime: '2025-06-04T13:47:17'
        },
        {
          id: 4,
          orderId: 'REC20231201004',
          account: 'user004',
          goldType: 'jewelry', // 黄金
          goldCondition: '完好',
          purity: '足金',
          estimatedWeight: 15.2,
          estimatedPrice: 2200.00,
          finalPrice: 2180.00,
          status: 4, // 已检测
          description: '黄金项链',
          inspectionResult: '成色符合标准，重量15.1g',
          receiverName: '赵六',
          receiverPhone: '***********',
          receiverAddress: '深圳市南山区xxx',
          createTime: '2025-06-01T13:47:17',
          updateTime: '2025-06-04T13:47:17'
        },
        {
          id: 5,
          orderId: 'REC20231201005',
          account: 'user005',
          goldType: 'bar', // 铂金
          goldCondition: '完好',
          purity: '999',
          estimatedWeight: 100.0,
          estimatedPrice: 14400.00,
          finalPrice: 14350.00,
          status: 5, // 已确认
          description: '铂金条',
          inspectionResult: '成色符合标准，重量99.8g',
          receiverName: '孙七',
          receiverPhone: '***********',
          receiverAddress: '杭州市西湖区xxx',
          createTime: '2025-05-30T13:47:17',
          updateTime: '2025-06-04T13:47:17'
        },
        {
          id: 6,
          orderId: 'REC20231201006',
          account: 'user006',
          goldType: 'other', // 其他
          goldCondition: '完好',
          purity: '足金',
          estimatedWeight: 8.5,
          estimatedPrice: 1200.00,
          finalPrice: 1180.00,
          status: 6, // 订单已完成
          description: '其他贵金属',
          inspectionResult: '成色符合标准，重量8.3g',
          receiverName: '周八',
          receiverPhone: '***********',
          receiverAddress: '成都市锦江区xxx',
          createTime: '2025-05-28T13:47:17',
          updateTime: '2025-06-04T13:47:17'
        },
        {
          id: 7,
          orderId: 'REC20231201007',
          account: 'user007',
          goldType: 'jewelry', // 黄金
          goldCondition: '完好',
          purity: '足金',
          estimatedWeight: 12.0,
          estimatedPrice: 1800.00,
          finalPrice: null,
          status: 0, // 已取消
          description: '黄金耳环',
          inspectionResult: '',
          receiverName: '李九',
          receiverPhone: '***********',
          receiverAddress: '武汉市洪山区xxx',
          createTime: '2025-05-26T13:47:17',
          updateTime: '2025-06-04T13:47:17'
        }
      ]
      total.value = 7
    } else {
      list.value = []
      total.value = 0
    }
  } finally {
    listLoading.value = false
  }
}

function handleFilter() {
  listQuery.value.pageNum = 1
  getList()
}

function handleReset() {
  listQuery.value = {
    pageNum: 1,
    pageSize: 20,
    keyword: '',
    status: '',
    goldType: '',
    estimatedPrice: null
  }
  getList()
}

function handleDetail(row) {
  router.push(`/order-detail/${row.orderId}`)
}

// 取件操作 - 状态从1变为2
async function handlePickup(row) {
  try {
    await ElMessageBox.confirm('确定已完成取件操作吗？', '确认取件', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await updateOrderStatus(row.orderId, 2)
    ElMessage.success('取件完成')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

// 检测操作 - 状态从2变为3
async function handleInspect(row) {
  try {
    await ElMessageBox.confirm('确定开始检测该订单吗？', '开始检测', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await updateOrderStatus(row.orderId, 3)
    ElMessage.success('已开始检测')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

// 上传检测结果 - 状态从3变为4
function handleUploadResult(row) {
  currentOrder.value = row
  processForm.value = {
    orderId: row.orderId,
    actualWeight: row.estimatedWeight,
    actualPurity: row.purity,
    finalPrice: row.estimatedPrice,
    inspectionResult: ''
  }
  processDialogVisible.value = true
}

async function confirmProcess() {
  try {
    await updateOrderStatus(currentOrder.value.orderId, 4)
    ElMessage.success('检测结果上传成功')
    processDialogVisible.value = false
    getList()
  } catch (error) {
    ElMessage.error('上传失败')
  }
}

// 完成订单 - 状态从5变为6
async function handleComplete(row) {
  try {
    await ElMessageBox.confirm('确定要完成该回收订单吗？', '完成订单', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await updateOrderStatus(row.orderId, 6)
    ElMessage.success('回收订单已完成')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

async function handleDownload() {
  try {
    const response = await exportOrders(listQuery.value)
    // 处理文件下载
    const blob = new Blob([response], { type: 'application/vnd.ms-excel' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `回收订单数据_${new Date().getTime()}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 已送到店功能
function handleDeliveredToStore(row) {
  deliveredForm.value = {
    orderId: row.orderId,
    finalPrice: row.estimatedPrice || null
  }
  deliveredDialogVisible.value = true
}

async function confirmDelivered() {
  // 验证表单
  if (!deliveredForm.value.finalPrice || deliveredForm.value.finalPrice <= 0) {
    ElMessage.error('请输入有效的最终价格')
    return
  }

  deliveredLoading.value = true
  try {
    // 第一步：设置最终价格
    await setFinalPrice(deliveredForm.value.orderId, deliveredForm.value.finalPrice)

    // 第二步：更新订单状态为已完成(6)
    await updateOrderStatus(deliveredForm.value.orderId, 6)

    ElMessage.success('操作成功，订单已完成')
    deliveredDialogVisible.value = false
    getList() // 刷新列表
  } catch (error) {
    console.error('已送到店操作失败:', error)
    ElMessage.error(`操作失败: ${error.response?.data?.message || error.message || '请稍后重试'}`)
  } finally {
    deliveredLoading.value = false
  }
}

function getStatusType(status) {
  const statusMap = {
    0: 'danger',     // 已取消
    1: 'primary',    // 已下单
    2: 'warning',    // 已取件
    3: 'info',       // 待检测
    4: 'success',    // 已检测
    5: 'primary',    // 已确认
    6: 'success'     // 订单已完成
  }
  return statusMap[status] || 'info'
}

function getStatusText(status) {
  const statusMap = {
    0: '已取消',
    1: '已下单',
    2: '已取件',
    3: '待检测',
    4: '已检测',
    5: '已确认',
    6: '订单已完成'
  }
  return statusMap[status] || '未知'
}

function getGoldTypeText(goldType) {
  const typeMap = {
    'jewelry': '黄金',
    'bar': '铂金',
    'broken': '钯金',
    'other': '其他'
  }
  return typeMap[goldType] || goldType
}

function formatTime(time) {
  if (!time) return '-'
  return parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}')
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.text-muted {
  color: #999;
}

.filter-container {
  padding-bottom: 10px;
}

.filter-item {
  margin-right: 10px;
}

.dialog-footer {
  text-align: right;
}

/* 表格加载状态优化 */
.el-table .el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
}
</style>
