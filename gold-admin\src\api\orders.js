import request from '@/utils/request'

// 获取订单列表
export function getOrderList(params) {
  return request({
    url: '/api/admin/orders/list',
    method: 'get',
    params
  })
}

// 获取订单详情
export function getOrderDetail(orderId) {
  return request({
    url: `/api/admin/orders/${orderId}`,
    method: 'get'
  })
}

// 更新订单状态
export function updateOrderStatus(orderId, status) {
  return request({
    url: `/api/admin/orders/${orderId}/status`,
    method: 'put',
    params: { status }
  })
}

// 设置最终价格
export function setFinalPrice(orderId, finalPrice) {
  return request({
    url: `/api/admin/orders/${orderId}/final-price`,
    method: 'put',
    params: { finalPrice }
  })
}

// 取件操作 - 填写快递信息
export function pickupOrder(orderId, expressData) {
  return request({
    url: `/api/admin/orders/${orderId}/pickup`,
    method: 'put',
    data: expressData
  })
}

// 更新订单信息
export function updateOrder(id, data) {
  return request({
    url: `/admin/orders/${id}`,
    method: 'put',
    data
  })
}

// 删除订单
export function deleteOrder(id) {
  return request({
    url: `/admin/orders/${id}`,
    method: 'delete'
  })
}

// 获取订单统计数据
export function getOrderStats(params) {
  return request({
    url: '/admin/orders/stats',
    method: 'get',
    params
  })
}

// 导出订单数据
export function exportOrders(params) {
  return request({
    url: '/admin/orders/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
