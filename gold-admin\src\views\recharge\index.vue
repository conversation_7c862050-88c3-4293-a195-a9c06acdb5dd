<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        placeholder="请输入用户账号或订单号"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter="handleFilter"
      />
      <el-select
        v-model="listQuery.type"
        placeholder="交易类型"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option label="充值" value="recharge" />
        <el-option label="提现" value="withdraw" />
      </el-select>
      <el-select
        v-model="listQuery.status"
        placeholder="状态"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option label="待处理" value="1" />
        <el-option label="处理中" value="2" />
        <el-option label="已完成" value="3" />
        <el-option label="已拒绝" value="4" />
      </el-select>
      <el-button class="filter-item" type="primary" icon="Search" @click="handleFilter">
        搜索
      </el-button>
      <el-button class="filter-item" type="success" icon="Download" @click="handleDownload">
        导出
      </el-button>
    </div>

    <el-table
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
      v-loading="listLoading"
    >
      <el-table-column label="订单号" width="180" align="center">
        <template #default="scope">
          <span>{{ scope.row.orderNo }}</span>
        </template>
      </el-table-column>

      <el-table-column label="用户账号" width="120" align="center">
        <template #default="scope">
          <span>{{ scope.row.account }}</span>
        </template>
      </el-table-column>

      <el-table-column label="交易类型" width="100" align="center">
        <template #default="scope">
          <el-tag :type="scope.row.type === 'recharge' ? 'success' : 'warning'">
            {{ scope.row.type === 'recharge' ? '充值' : '提现' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="金额" width="120" align="right">
        <template #default="scope">
          ¥{{ scope.row.amount }}
        </template>
      </el-table-column>

      <el-table-column label="手续费" width="100" align="right">
        <template #default="scope">
          <span v-if="scope.row.fee">¥{{ scope.row.fee }}</span>
          <span v-else class="text-muted">-</span>
        </template>
      </el-table-column>

      <el-table-column label="实际金额" width="120" align="right">
        <template #default="scope">
          ¥{{ scope.row.actualAmount }}
        </template>
      </el-table-column>

      <el-table-column label="支付方式" width="120" align="center">
        <template #default="scope">
          {{ getPaymentMethodText(scope.row.paymentMethod) }}
        </template>
      </el-table-column>

      <el-table-column label="状态" width="100" align="center">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="创建时间" width="160">
        <template #default="scope">
          {{ formatTime(scope.row.createTime) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button type="primary" size="small" @click="handleDetail(scope.row)">
            详情
          </el-button>
          <el-button
            v-if="scope.row.status === 1"
            size="small"
            type="success"
            @click="handleApprove(scope.row)"
          >
            通过
          </el-button>
          <el-button
            v-if="scope.row.status === 1"
            size="small"
            type="danger"
            @click="handleReject(scope.row)"
          >
            拒绝
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <!-- 详情对话框 -->
    <el-dialog
      title="交易详情"
      v-model="detailDialogVisible"
      width="600px"
    >
      <el-descriptions :column="2" border v-if="currentRecord">
        <el-descriptions-item label="订单号">{{ currentRecord.orderNo }}</el-descriptions-item>
        <el-descriptions-item label="用户账号">{{ currentRecord.account }}</el-descriptions-item>
        <el-descriptions-item label="交易类型">
          <el-tag :type="currentRecord.type === 'recharge' ? 'success' : 'warning'">
            {{ currentRecord.type === 'recharge' ? '充值' : '提现' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="金额">¥{{ currentRecord.amount }}</el-descriptions-item>
        <el-descriptions-item label="手续费">
          <span v-if="currentRecord.fee">¥{{ currentRecord.fee }}</span>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="实际金额">¥{{ currentRecord.actualAmount }}</el-descriptions-item>
        <el-descriptions-item label="支付方式">{{ getPaymentMethodText(currentRecord.paymentMethod) }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(currentRecord.status)">
            {{ getStatusText(currentRecord.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间" :span="2">{{ formatTime(currentRecord.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ currentRecord.remark || '-' }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog
      :title="approveType === 'approve' ? '审核通过' : '审核拒绝'"
      v-model="approveDialogVisible"
      width="500px"
    >
      <el-form
        ref="approveFormRef"
        :model="approveForm"
        label-width="100px"
      >
        <el-form-item label="处理备注">
          <el-input
            v-model="approveForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入处理备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="approveDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmApprove">确认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getRechargeList, updateRechargeStatus, exportRecharge } from '@/api/recharge'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination/index.vue'

const list = ref([])
const total = ref(0)
const listLoading = ref(true)
const listQuery = ref({
  page: 1,
  limit: 20,
  keyword: '',
  type: '',
  status: ''
})

const detailDialogVisible = ref(false)
const approveDialogVisible = ref(false)
const currentRecord = ref(null)
const approveType = ref('')
const approveForm = ref({
  remark: ''
})

async function getList() {
  listLoading.value = true
  try {
    const response = await getRechargeList(listQuery.value)
    list.value = response.data.list
    total.value = response.data.total
  } catch (error) {
    console.error('获取充值提现列表失败:', error)
    // 模拟数据
    list.value = [
      {
        id: 1,
        orderNo: 'RC20231201001',
        account: 'user001',
        type: 'recharge',
        amount: 1000.00,
        fee: 0,
        actualAmount: 1000.00,
        paymentMethod: 'alipay',
        status: 3,
        createTime: new Date().getTime(),
        remark: '支付宝充值'
      },
      {
        id: 2,
        orderNo: 'WD20231201001',
        account: 'user002',
        type: 'withdraw',
        amount: 500.00,
        fee: 5.00,
        actualAmount: 495.00,
        paymentMethod: 'bank',
        status: 1,
        createTime: new Date().getTime() - ********,
        remark: '银行卡提现'
      }
    ]
    total.value = 2
  } finally {
    listLoading.value = false
  }
}

function handleFilter() {
  listQuery.value.page = 1
  getList()
}

function handleDetail(row) {
  currentRecord.value = row
  detailDialogVisible.value = true
}

function handleApprove(row) {
  currentRecord.value = row
  approveType.value = 'approve'
  approveForm.value.remark = ''
  approveDialogVisible.value = true
}

function handleReject(row) {
  currentRecord.value = row
  approveType.value = 'reject'
  approveForm.value.remark = ''
  approveDialogVisible.value = true
}

async function confirmApprove() {
  try {
    const status = approveType.value === 'approve' ? 3 : 4
    await updateRechargeStatus(currentRecord.value.id, {
      status,
      remark: approveForm.value.remark
    })
    ElMessage.success(approveType.value === 'approve' ? '审核通过成功' : '审核拒绝成功')
    approveDialogVisible.value = false
    getList()
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

async function handleDownload() {
  try {
    const response = await exportRecharge(listQuery.value)
    const blob = new Blob([response], { type: 'application/vnd.ms-excel' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `充值提现数据_${new Date().getTime()}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

function getStatusType(status) {
  const statusMap = {
    1: 'warning',
    2: 'primary',
    3: 'success',
    4: 'danger'
  }
  return statusMap[status] || 'info'
}

function getStatusText(status) {
  const statusMap = {
    1: '待处理',
    2: '处理中',
    3: '已完成',
    4: '已拒绝'
  }
  return statusMap[status] || '未知'
}

function getPaymentMethodText(method) {
  const methodMap = {
    alipay: '支付宝',
    wechat: '微信支付',
    bank: '银行卡',
    balance: '余额'
  }
  return methodMap[method] || method
}

function formatTime(time) {
  return parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}')
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.text-muted {
  color: #999;
}

.filter-container {
  padding-bottom: 10px;
}

.filter-item {
  margin-right: 10px;
}

.dialog-footer {
  text-align: right;
}
</style>
