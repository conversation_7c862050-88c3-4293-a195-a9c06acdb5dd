<template>
  <div class="app-container">
    <el-card v-loading="loading">
      <template #header>
        <div class="card-header">
          <span>订单详情</span>
          <el-button type="primary" @click="$router.go(-1)">返回</el-button>
        </div>
      </template>

      <div v-if="orderDetail" class="order-detail">
        <!-- 基本信息 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-descriptions title="订单信息" :column="1" border>
              <el-descriptions-item label="订单号">
                {{ orderDetail.orderId }}
              </el-descriptions-item>
              <el-descriptions-item label="用户账号">
                {{ orderDetail.account }}
              </el-descriptions-item>
              <el-descriptions-item label="订单状态">
                <el-tag :type="getStatusType(orderDetail.status)">
                  {{ getStatusText(orderDetail.status) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="创建时间">
                {{ formatTime(orderDetail.createTime) }}
              </el-descriptions-item>
              <el-descriptions-item label="更新时间">
                {{ formatTime(orderDetail.updateTime) }}
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
          
          <el-col :span="12">
            <el-descriptions title="收货信息" :column="1" border>
              <el-descriptions-item label="收货人">
                {{ orderDetail.receiverName }}
              </el-descriptions-item>
              <el-descriptions-item label="联系电话">
                {{ orderDetail.receiverPhone }}
              </el-descriptions-item>
              <el-descriptions-item label="收货地址">
                {{ orderDetail.receiverAddress }}
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
        </el-row>

        <!-- 贵金属信息 -->
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="12">
            <el-descriptions title="贵金属信息" :column="1" border>
              <el-descriptions-item label="贵金属类型">
                {{ getGoldTypeText(orderDetail.goldType) }}
              </el-descriptions-item>
              <el-descriptions-item label="贵金属状况">
                {{ orderDetail.goldCondition }}
              </el-descriptions-item>
              <el-descriptions-item label="成色">
                {{ orderDetail.purity }}
              </el-descriptions-item>
              <el-descriptions-item label="描述">
                {{ orderDetail.description }}
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
          
          <el-col :span="12">
            <el-descriptions title="价格信息" :column="1" border>
              <el-descriptions-item label="预估重量">
                {{ orderDetail.estimatedWeight }}g
              </el-descriptions-item>
              <el-descriptions-item label="预估价格">
                ¥{{ formatPrice(orderDetail.estimatedPrice) }}
              </el-descriptions-item>
              <el-descriptions-item label="最终价格">
                <span v-if="orderDetail.finalPrice">¥{{ formatPrice(orderDetail.finalPrice) }}</span>
                <span v-else class="text-muted">待定</span>
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
        </el-row>

        <!-- 检测结果 -->
        <div v-if="orderDetail.inspectionResult" style="margin-top: 20px;">
          <el-descriptions title="检测结果" :column="1" border>
            <el-descriptions-item label="检测结果">
              {{ orderDetail.inspectionResult }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 图片展示 -->
        <div v-if="orderDetail.imageList && orderDetail.imageList.length > 0" style="margin-top: 20px;">
          <h3>商品图片</h3>
          <div class="image-gallery">
            <el-image
              v-for="(image, index) in orderDetail.imageList"
              :key="index"
              :src="image"
              style="width: 200px; height: 200px; margin: 10px;"
              :preview-src-list="orderDetail.imageList"
              fit="cover"
            />
          </div>
        </div>
        <!-- 兼容旧的单图片字段 -->
        <div v-else-if="orderDetail.imageBase64" style="margin-top: 20px;">
          <h3>商品图片</h3>
          <el-image
            :src="orderDetail.imageBase64"
            style="width: 200px; height: 200px; margin: 10px;"
            :preview-src-list="[orderDetail.imageBase64]"
            fit="cover"
          />
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons" style="margin-top: 30px;">
          <el-button
            v-if="orderDetail.status === 1"
            type="success"
            @click="handlePickup"
          >
            取件
          </el-button>
          <el-button
            v-if="orderDetail.status === 2"
            type="warning"
            @click="handleInspect"
          >
            检测
          </el-button>
          <el-button
            v-if="orderDetail.status === 3"
            type="primary"
            @click="handleUploadResult"
          >
            上传结果
          </el-button>
          <el-button
            v-if="orderDetail.status === 5"
            type="success"
            @click="handleComplete"
          >
            完成订单
          </el-button>
          <!-- 已送到店按钮 - 所有状态都显示 -->
          <el-button
            type="success"
            @click="handleDeliveredToStore"
          >
            已送到店
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 上传检测结果对话框 -->
    <el-dialog
      title="上传检测结果"
      v-model="processDialogVisible"
      width="500px"
    >
      <el-form
        ref="processFormRef"
        :model="processForm"
        label-width="100px"
      >
        <el-form-item label="实际重量(g)">
          <el-input-number
            v-model="processForm.actualWeight"
            :precision="2"
            :min="0"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="实际成色">
          <el-input v-model="processForm.actualPurity" />
        </el-form-item>
        <el-form-item label="最终价格">
          <el-input-number
            v-model="processForm.finalPrice"
            :precision="2"
            :min="0"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="检测结果">
          <el-input
            v-model="processForm.inspectionResult"
            type="textarea"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="processDialogVisible = false">
            取消
          </el-button>
          <el-button type="primary" @click="confirmProcess">
            上传结果
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 已送到店对话框 -->
    <el-dialog
      title="设置最终价格"
      v-model="deliveredDialogVisible"
      width="400px"
    >
      <el-form
        ref="deliveredFormRef"
        :model="deliveredForm"
        :rules="deliveredRules"
        label-width="100px"
      >
        <el-form-item label="订单号">
          <el-input v-model="deliveredForm.orderId" readonly />
        </el-form-item>
        <el-form-item label="最终价格" prop="finalPrice">
          <el-input-number
            v-model="deliveredForm.finalPrice"
            :precision="2"
            :min="0"
            style="width: 100%"
            placeholder="请输入最终价格"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="deliveredDialogVisible = false">
            取消
          </el-button>
          <el-button type="primary" @click="confirmDelivered" :loading="deliveredLoading">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getOrderDetail, updateOrderStatus, setFinalPrice } from '@/api/orders'
import { parseTime } from '@/utils'

const route = useRoute()

const loading = ref(true)
const orderDetail = ref(null)
const processDialogVisible = ref(false)
const processForm = ref({
  actualWeight: 0,
  actualPurity: '',
  finalPrice: 0,
  inspectionResult: ''
})

// 已送到店对话框相关
const deliveredDialogVisible = ref(false)
const deliveredLoading = ref(false)
const deliveredForm = ref({
  orderId: '',
  finalPrice: null
})
const deliveredRules = {
  finalPrice: [
    { required: true, message: '请输入最终价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '最终价格必须大于等于0', trigger: 'blur' }
  ]
}

async function fetchOrderDetail() {
  loading.value = true
  try {
    const response = await getOrderDetail(route.params.id)
    orderDetail.value = response.data
    console.log('获取订单详情成功:', orderDetail.value)
  } catch (error) {
    console.error('获取订单详情失败:', error)
    ElMessage.error(`获取订单详情失败: ${error.response?.data?.message || error.message || '请稍后重试'}`)

    // 开发环境下显示模拟数据
    if (process.env.NODE_ENV === 'development') {
      orderDetail.value = {
        id: 1,
        orderId: route.params.id,
        account: 'user001',
        goldType: 'jewelry', // 使用新的映射值
        goldCondition: '完好',
        purity: '足金',
        estimatedWeight: 10.5,
        estimatedPrice: 1500.00,
        finalPrice: null,
        status: 1,
        description: '黄金戒指，款式经典',
        inspectionResult: '',
        receiverName: '张三',
        receiverPhone: '***********',
        receiverAddress: '北京市朝阳区xxx街道xxx号',
        imageList: ['https://via.placeholder.com/200x200', 'https://via.placeholder.com/200x200'],
        createTime: '2025-06-04T13:47:17',
        updateTime: '2025-06-04T13:47:17'
      }
    }
  } finally {
    loading.value = false
  }
}

// 取件操作 - 状态从1变为2
async function handlePickup() {
  try {
    await ElMessageBox.confirm('确定已完成取件操作吗？', '确认取件', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await updateOrderStatus(orderDetail.value.orderId, 2)
    ElMessage.success('取件完成')
    fetchOrderDetail()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

// 检测操作 - 状态从2变为3
async function handleInspect() {
  try {
    await ElMessageBox.confirm('确定开始检测该订单吗？', '开始检测', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await updateOrderStatus(orderDetail.value.orderId, 3)
    ElMessage.success('已开始检测')
    fetchOrderDetail()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

// 上传检测结果 - 状态从3变为4
function handleUploadResult() {
  processForm.value = {
    actualWeight: orderDetail.value.estimatedWeight,
    actualPurity: orderDetail.value.purity,
    finalPrice: orderDetail.value.estimatedPrice,
    inspectionResult: ''
  }
  processDialogVisible.value = true
}

async function confirmProcess() {
  try {
    await updateOrderStatus(orderDetail.value.orderId, 4)
    ElMessage.success('检测结果上传成功')
    processDialogVisible.value = false
    fetchOrderDetail()
  } catch (error) {
    ElMessage.error('上传失败')
  }
}

// 完成订单 - 状态从5变为6
async function handleComplete() {
  try {
    await ElMessageBox.confirm('确定要完成该订单吗？', '完成订单', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await updateOrderStatus(orderDetail.value.orderId, 6)
    ElMessage.success('订单已完成')
    fetchOrderDetail()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

// 已送到店功能
function handleDeliveredToStore() {
  deliveredForm.value = {
    orderId: orderDetail.value.orderId,
    finalPrice: orderDetail.value.estimatedPrice || null
  }
  deliveredDialogVisible.value = true
}

async function confirmDelivered() {
  // 验证表单
  if (!deliveredForm.value.finalPrice || deliveredForm.value.finalPrice <= 0) {
    ElMessage.error('请输入有效的最终价格')
    return
  }

  deliveredLoading.value = true
  try {
    // 第一步：设置最终价格
    await setFinalPrice(orderDetail.value.orderId, deliveredForm.value.finalPrice)

    // 第二步：更新订单状态为已完成(6)
    await updateOrderStatus(orderDetail.value.orderId, 6)

    ElMessage.success('操作成功，订单已完成')
    deliveredDialogVisible.value = false
    fetchOrderDetail()
  } catch (error) {
    console.error('已送到店操作失败:', error)
    ElMessage.error(`操作失败: ${error.response?.data?.message || error.message || '请稍后重试'}`)
  } finally {
    deliveredLoading.value = false
  }
}

function getStatusType(status) {
  const statusMap = {
    1: 'primary',    // 已下单
    2: 'warning',    // 已取件
    3: 'info',       // 待检测
    4: 'success',    // 已检测
    5: 'primary',    // 已确认
    6: 'success'     // 订单已完成
  }
  return statusMap[status] || 'info'
}

function getStatusText(status) {
  const statusMap = {
    1: '已下单',
    2: '已取件',
    3: '待检测',
    4: '已检测',
    5: '已确认',
    6: '订单已完成'
  }
  return statusMap[status] || '未知'
}

function getGoldTypeText(goldType) {
  const typeMap = {
    'jewelry': '黄金',
    'bar': '铂金',
    'broken': '钯金',
    'other': '其他'
  }
  return typeMap[goldType] || goldType
}

function formatPrice(price) {
  if (price === null || price === undefined) return '0.00'
  return Number(price).toFixed(2)
}

function formatTime(time) {
  if (!time) return '-'
  return parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}')
}

onMounted(() => {
  fetchOrderDetail()
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.text-muted {
  color: #999;
}

.action-buttons {
  text-align: center;
}

.action-buttons .el-button {
  margin: 0 5px;
}

.image-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.dialog-footer {
  text-align: right;
}
</style>
