<template>
  <div class="app-container">
    <div class="fee-header">
      <h2>手续费管理</h2>
      <p>设置各种交易的手续费率和固定费用</p>
    </div>

    <el-row :gutter="20">
      <!-- 提现手续费 -->
      <el-col :span="12">
        <el-card class="fee-card">
          <template #header>
            <div class="card-header">
              <span>提现手续费</span>
              <el-icon class="withdraw-icon"><CreditCard /></el-icon>
            </div>
          </template>
          
          <el-form
            ref="withdrawFormRef"
            :model="withdrawForm"
            :rules="feeRules"
            label-width="120px"
          >
            <el-form-item label="手续费类型">
              <el-radio-group v-model="withdrawForm.type">
                <el-radio label="rate">按比例收费</el-radio>
                <el-radio label="fixed">固定费用</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item v-if="withdrawForm.type === 'rate'" label="手续费率" prop="rate">
              <el-input-number
                v-model="withdrawForm.rate"
                :precision="2"
                :min="0"
                :max="100"
                style="width: 100%"
              />
              <span class="unit">%</span>
            </el-form-item>
            
            <el-form-item v-if="withdrawForm.type === 'fixed'" label="固定费用" prop="fixedFee">
              <el-input-number
                v-model="withdrawForm.fixedFee"
                :precision="2"
                :min="0"
                style="width: 100%"
              />
              <span class="unit">元</span>
            </el-form-item>
            
            <el-form-item label="最低手续费" prop="minFee">
              <el-input-number
                v-model="withdrawForm.minFee"
                :precision="2"
                :min="0"
                style="width: 100%"
              />
              <span class="unit">元</span>
            </el-form-item>
            
            <el-form-item label="最高手续费" prop="maxFee">
              <el-input-number
                v-model="withdrawForm.maxFee"
                :precision="2"
                :min="0"
                style="width: 100%"
              />
              <span class="unit">元</span>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="saveWithdrawFee" :loading="withdrawLoading">
                保存设置
              </el-button>
              <el-button @click="resetWithdrawForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 交易手续费 -->
      <el-col :span="12">
        <el-card class="fee-card">
          <template #header>
            <div class="card-header">
              <span>交易手续费</span>
              <el-icon class="trade-icon"><Money /></el-icon>
            </div>
          </template>
          
          <el-form
            ref="tradeFormRef"
            :model="tradeForm"
            :rules="feeRules"
            label-width="120px"
          >
            <el-form-item label="买入手续费率" prop="buyRate">
              <el-input-number
                v-model="tradeForm.buyRate"
                :precision="2"
                :min="0"
                :max="100"
                style="width: 100%"
              />
              <span class="unit">%</span>
            </el-form-item>
            
            <el-form-item label="卖出手续费率" prop="sellRate">
              <el-input-number
                v-model="tradeForm.sellRate"
                :precision="2"
                :min="0"
                :max="100"
                style="width: 100%"
              />
              <span class="unit">%</span>
            </el-form-item>
            
            <el-form-item label="最低交易费" prop="minTradeFee">
              <el-input-number
                v-model="tradeForm.minTradeFee"
                :precision="2"
                :min="0"
                style="width: 100%"
              />
              <span class="unit">元</span>
            </el-form-item>
            
            <el-form-item label="VIP折扣率" prop="vipDiscount">
              <el-input-number
                v-model="tradeForm.vipDiscount"
                :precision="1"
                :min="0"
                :max="10"
                style="width: 100%"
              />
              <span class="unit">折</span>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="saveTradeFee" :loading="tradeLoading">
                保存设置
              </el-button>
              <el-button @click="resetTradeForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>

    <!-- 手续费统计 -->
    <el-card class="stats-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>手续费统计</span>
          <el-button type="primary" size="small" @click="refreshStats">
            刷新
          </el-button>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">¥{{ feeStats.todayFee }}</div>
            <div class="stat-label">今日手续费</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">¥{{ feeStats.monthFee }}</div>
            <div class="stat-label">本月手续费</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">¥{{ feeStats.totalFee }}</div>
            <div class="stat-label">累计手续费</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ feeStats.feeOrders }}</div>
            <div class="stat-label">收费订单数</div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 手续费记录 -->
    <el-card class="records-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>手续费记录</span>
          <el-button type="primary" size="small" @click="handleDownload">
            导出记录
          </el-button>
        </div>
      </template>
      
      <el-table :data="feeRecords" border style="width: 100%">
        <el-table-column prop="orderNo" label="订单号" width="180" />
        <el-table-column prop="account" label="用户账号" width="120" />
        <el-table-column prop="type" label="类型" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getFeeTypeColor(scope.row.type)">
              {{ getFeeTypeText(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="交易金额" width="120" align="right">
          <template #default="scope">
            ¥{{ scope.row.amount }}
          </template>
        </el-table-column>
        <el-table-column prop="feeRate" label="费率" width="80" align="center">
          <template #default="scope">
            {{ scope.row.feeRate }}%
          </template>
        </el-table-column>
        <el-table-column prop="feeAmount" label="手续费" width="100" align="right">
          <template #default="scope">
            ¥{{ scope.row.feeAmount }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="时间" width="160">
          <template #default="scope">
            {{ formatTime(scope.row.createTime) }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { CreditCard, Money } from '@element-plus/icons-vue'
import { getFeeConfig, updateFeeConfig, getFeeStats, getFeeRecords, exportFeeRecords } from '@/api/fee'
import { parseTime } from '@/utils'

const withdrawFormRef = ref()
const tradeFormRef = ref()
const withdrawLoading = ref(false)
const tradeLoading = ref(false)

const withdrawForm = ref({
  type: 'rate',
  rate: 0,
  fixedFee: 0,
  minFee: 0,
  maxFee: 0
})

const tradeForm = ref({
  buyRate: 0,
  sellRate: 0,
  minTradeFee: 0,
  vipDiscount: 10
})

const feeStats = ref({
  todayFee: 0,
  monthFee: 0,
  totalFee: 0,
  feeOrders: 0
})

const feeRecords = ref([])

const feeRules = {
  rate: [
    { required: true, message: '请输入手续费率', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '费率范围0-100%', trigger: 'blur' }
  ],
  fixedFee: [
    { required: true, message: '请输入固定费用', trigger: 'blur' },
    { type: 'number', min: 0, message: '费用不能小于0', trigger: 'blur' }
  ],
  minFee: [
    { type: 'number', min: 0, message: '费用不能小于0', trigger: 'blur' }
  ],
  maxFee: [
    { type: 'number', min: 0, message: '费用不能小于0', trigger: 'blur' }
  ],
  buyRate: [
    { required: true, message: '请输入买入手续费率', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '费率范围0-100%', trigger: 'blur' }
  ],
  sellRate: [
    { required: true, message: '请输入卖出手续费率', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '费率范围0-100%', trigger: 'blur' }
  ]
}

async function loadFeeConfig() {
  try {
    const response = await getFeeConfig()
    withdrawForm.value = response.data.withdraw || withdrawForm.value
    tradeForm.value = response.data.trade || tradeForm.value
  } catch (error) {
    console.error('加载手续费配置失败:', error)
    // 模拟数据
    withdrawForm.value = {
      type: 'rate',
      rate: 0.5,
      fixedFee: 5,
      minFee: 2,
      maxFee: 50
    }
    tradeForm.value = {
      buyRate: 0.1,
      sellRate: 0.1,
      minTradeFee: 1,
      vipDiscount: 8.5
    }
  }
}

async function saveWithdrawFee() {
  if (!withdrawFormRef.value) return
  
  try {
    await withdrawFormRef.value.validate()
    withdrawLoading.value = true
    
    await updateFeeConfig('withdraw', withdrawForm.value)
    ElMessage.success('提现手续费设置保存成功')
  } catch (error) {
    if (error !== false) {
      ElMessage.error('保存失败')
    }
  } finally {
    withdrawLoading.value = false
  }
}

async function saveTradeFee() {
  if (!tradeFormRef.value) return
  
  try {
    await tradeFormRef.value.validate()
    tradeLoading.value = true
    
    await updateFeeConfig('trade', tradeForm.value)
    ElMessage.success('交易手续费设置保存成功')
  } catch (error) {
    if (error !== false) {
      ElMessage.error('保存失败')
    }
  } finally {
    tradeLoading.value = false
  }
}

function resetWithdrawForm() {
  withdrawFormRef.value?.resetFields()
}

function resetTradeForm() {
  tradeFormRef.value?.resetFields()
}

async function refreshStats() {
  try {
    const response = await getFeeStats()
    feeStats.value = response.data
  } catch (error) {
    console.error('加载手续费统计失败:', error)
    // 模拟数据
    feeStats.value = {
      todayFee: 125.50,
      monthFee: 3456.78,
      totalFee: 45678.90,
      feeOrders: 1234
    }
  }
}

async function loadFeeRecords() {
  try {
    const response = await getFeeRecords()
    feeRecords.value = response.data.list || []
  } catch (error) {
    console.error('加载手续费记录失败:', error)
    // 模拟数据
    feeRecords.value = [
      {
        id: 1,
        orderNo: 'WD20231201001',
        account: 'user001',
        type: 'withdraw',
        amount: 1000.00,
        feeRate: 0.5,
        feeAmount: 5.00,
        createTime: new Date().getTime()
      },
      {
        id: 2,
        orderNo: 'TR20231201001',
        account: 'user002',
        type: 'trade',
        amount: 500.00,
        feeRate: 0.1,
        feeAmount: 0.50,
        createTime: new Date().getTime() - ********
      }
    ]
  }
}

async function handleDownload() {
  try {
    const response = await exportFeeRecords()
    const blob = new Blob([response], { type: 'application/vnd.ms-excel' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `手续费记录_${new Date().getTime()}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

function getFeeTypeColor(type) {
  const colorMap = {
    withdraw: 'warning',
    trade: 'success',
    recharge: 'primary'
  }
  return colorMap[type] || 'info'
}

function getFeeTypeText(type) {
  const textMap = {
    withdraw: '提现',
    trade: '交易',
    recharge: '充值'
  }
  return textMap[type] || type
}

function formatTime(time) {
  return parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}')
}

onMounted(() => {
  loadFeeConfig()
  refreshStats()
  loadFeeRecords()
})
</script>

<style scoped>
.fee-header {
  margin-bottom: 20px;
}

.fee-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.fee-header p {
  margin: 0;
  color: #909399;
}

.fee-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.withdraw-icon {
  color: #e6a23c;
  font-size: 20px;
}

.trade-icon {
  color: #67c23a;
  font-size: 20px;
}

.unit {
  margin-left: 8px;
  color: #909399;
  font-size: 12px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}
</style>
