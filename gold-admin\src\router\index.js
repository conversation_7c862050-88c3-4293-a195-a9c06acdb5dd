import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/layout/index.vue'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: { title: '登录', hidden: true }
  },
  {
    path: '',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: { title: '仪表盘', icon: 'Monitor' }
      }
    ]
  },
  {
    path: '/users',
    component: Layout,
    children: [
      {
        path: '',
        name: 'Users',
        component: () => import('@/views/users/index.vue'),
        meta: { title: '用户管理', icon: 'User' }
      }
    ]
  },
  {
    path: '/orders',
    component: Layout,
    children: [
      {
        path: '',
        name: 'RecycleOrders',
        component: () => import('@/views/orders/recycle.vue'),
        meta: { title: '回收订单', icon: 'Document' }
      }
    ]
  },
  {
    path: '/price',
    component: Layout,
    children: [
      {
        path: '',
        name: 'Price',
        component: () => import('@/views/price/index.vue'),
        meta: { title: '价格管理', icon: 'Money' }
      }
    ]
  },
  {
    path: '/recharge',
    component: Layout,
    children: [
      {
        path: '',
        name: 'Recharge',
        component: () => import('@/views/recharge/index.vue'),
        meta: { title: '充值/提现管理', icon: 'CreditCard' }
      }
    ]
  },
  {
    path: '/fee',
    component: Layout,
    children: [
      {
        path: '',
        name: 'Fee',
        component: () => import('@/views/fee/index.vue'),
        meta: { title: '手续费管理', icon: 'Coin' }
      }
    ]
  },
  {
    path: '/rebate',
    component: Layout,
    children: [
      {
        path: '',
        name: 'Rebate',
        component: () => import('@/views/rebate/index.vue'),
        meta: { title: '返点管理', icon: 'Present' }
      }
    ]
  },
  {
    path: '/lock-orders',
    component: Layout,
    children: [
      {
        path: '',
        name: 'LockOrders',
        component: () => import('@/views/lock-orders/index.vue'),
        meta: { title: '锁价回收订单管理', icon: 'Lock' }
      }
    ]
  },
  {
    path: '/products',
    component: Layout,
    children: [
      {
        path: '',
        name: 'Products',
        component: () => import('@/views/products/index.vue'),
        meta: { title: '产品管理', icon: 'Goods' }
      }
    ]
  },
  {
    path: '/mall-orders',
    component: Layout,
    children: [
      {
        path: '',
        name: 'MallOrders',
        component: () => import('@/views/mall-orders/index.vue'),
        meta: { title: '商城订单管理', icon: 'ShoppingCart' }
      }
    ]
  },
  {
    path: '/settings',
    component: Layout,
    children: [
      {
        path: '',
        name: 'Settings',
        component: () => import('@/views/settings/index.vue'),
        meta: { title: '系统设置', icon: 'Setting' }
      }
    ]
  },
  // 订单详情页面（隐藏，不在侧边栏显示）
  {
    path: '/order-detail/:id',
    component: Layout,
    meta: { hidden: true },
    children: [
      {
        path: '',
        name: 'OrderDetail',
        component: () => import('@/views/orders/detail.vue'),
        meta: { title: '订单详情', hidden: true }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('admin_token')

  if (to.path === '/login') {
    if (token) {
      next('/')
    } else {
      next()
    }
  } else {
    if (token) {
      next()
    } else {
      next('/login')
    }
  }
})

export default router
